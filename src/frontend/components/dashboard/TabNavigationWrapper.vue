<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import TabNavigation from './TabNavigation.vue'
import { useMetrics } from '@composables/useMetrics'
import { useDataRefresh } from '@composables/useDataRefresh'

// Props from server-side data
interface DashboardData {
  activeTab: string
  counts: {
    domains: number
    aliases: number
    webhooks: number
  }
}

// Use metrics system for reactive counts
const { counts: metricsCount, loadMetrics } = useMetrics()
const { refreshState } = useDataRefresh()

// Initialize with server-side data, then switch to reactive metrics
const dashboardData = ref<DashboardData>({
  activeTab: (window as any).dashboardData?.activeTab || 'domains',
  counts: (window as any).dashboardData?.counts || { domains: 0, aliases: 0, webhooks: 0 }
})

// Watch for data refresh triggers and update counts
watch([() => refreshState.domains, () => refreshState.aliases, () => refreshState.webhooks], async () => {
  try {
    await loadMetrics()
    // Update counts from metrics
    dashboardData.value.counts = {
      domains: metricsCount.value.domains,
      aliases: metricsCount.value.aliases,
      webhooks: metricsCount.value.webhooks
    }
  } catch (error) {
    console.error('Failed to refresh counts:', error)
  }
}, { immediate: false })

// Methods
const handleCreateAction = (actionType: string) => {
  // Use the existing global modal system
  if ((window as any).openModal) {
    (window as any).openModal(actionType)
  } else {
    console.warn('Modal system not available')
  }
}

onMounted(async () => {
  // Load initial metrics to get accurate counts
  try {
    await loadMetrics()
    dashboardData.value.counts = {
      domains: metricsCount.value.domains,
      aliases: metricsCount.value.aliases,
      webhooks: metricsCount.value.webhooks
    }
  } catch (error) {
    console.error('Failed to load initial metrics:', error)
    // Keep server-side counts as fallback
  }
})
</script>

<template>
  <TabNavigation
    :counts="dashboardData.counts"
    @create-action="handleCreateAction"
  />
</template>
