<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import TabNavigation from './TabNavigation.vue'
import { useMetrics } from '@composables/useMetrics'
import { useDataRefresh } from '@composables/useDataRefresh'

// Use metrics system for reactive counts
const { counts: metricsCount, refreshMetrics } = useMetrics()
const { refreshState } = useDataRefresh()

// Use computed counts directly from metrics instead of local state
const counts = computed(() => {
  console.log('Computing counts from metrics:', metricsCount.value)
  return metricsCount.value
})

// Watch for data refresh triggers and update counts
watch([() => refreshState.domains, () => refreshState.aliases, () => refreshState.webhooks], async () => {
  console.log('Data refresh triggered, refreshing metrics...')
  try {
    await refreshMetrics()
    console.log('Metrics refreshed, new counts:', metricsCount.value)
  } catch (error) {
    console.error('Failed to refresh metrics:', error)
  }
}, { immediate: false })

// Methods
const handleCreateAction = (actionType: string) => {
  // Use the existing global modal system
  if ((window as any).openModal) {
    (window as any).openModal(actionType)
  } else {
    console.warn('Modal system not available')
  }
}

onMounted(async () => {
  // Load initial metrics to get accurate counts
  console.log('TabNavigationWrapper mounted, loading metrics...')
  console.log('Server-side data available:', (window as any).dashboardData)
  console.log('Initial counts before metrics:', counts.value)
  try {
    await refreshMetrics()
    console.log('Initial metrics loaded:', metricsCount.value)
    console.log('Computed counts after metrics:', counts.value)
  } catch (error) {
    console.error('Failed to load initial metrics:', error)
  }
})
</script>

<template>
  <TabNavigation
    :counts="counts"
    @create-action="handleCreateAction"
  />
</template>
