<template>
  <div class="min-h-screen bg-white">
    <!-- User Header Component -->
    <UserHeader />

    <!-- Dashboard Content with Metrics and Navigation -->
    <div class="bg-white overflow-x-hidden">
      <!-- Metrics Pill and Tab Navigation (only for dashboard pages) -->
      <template v-if="!isSettingsPage">
        <!-- Metrics Pill -->
        <MetricsPill />

        <!-- Tab Navigation -->
        <TabNavigation
          :counts="counts"
          @create-action="handleCreateAction"
        />
      </template>

      <!-- Main Content -->
      <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6 pb-8">
        <slot />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import UserHeader from '../components/layout/UserHeader.vue'
import TabNavigation from '../components/dashboard/TabNavigation.vue'
import MetricsPill from '../components/dashboard/MetricsPill.vue'

// Vue Router
const route = useRoute()

// State for dashboard data
const counts = ref({
  domains: 0,
  aliases: 0,
  webhooks: 0
})

// Detect if we're on the settings page using Vue Router
const isSettingsPage = computed(() => {
  return route.name === 'settings'
})

// Load dashboard counts
const loadDashboardData = async () => {
  try {
    const response = await fetch('/api/dashboard/metrics')
    if (response.ok) {
      const data = await response.json()
      counts.value = {
        domains: data.domains || 0,
        aliases: data.aliases || 0,
        webhooks: data.webhooks || 0
      }
    }
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
  }
}

// Handle create actions from tab navigation
const handleCreateAction = (actionType: string) => {
  console.log('Create action:', actionType)

  // Use the global modal system set up by App
  const tryOpenModal = () => {
    if ((window as any).openModal) {
      (window as any).openModal(actionType)
      return true
    } else if ((window as any).vueModal) {
      (window as any).vueModal.open(actionType)
      return true
    }
    return false
  }

  // Try immediately, if not available, wait a bit and try again
  if (!tryOpenModal()) {
    console.warn('Modal system not available yet, retrying...')
    setTimeout(() => {
      if (!tryOpenModal()) {
        console.error('Modal system still not available after retry')
      }
    }, 100)
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
/* User layout specific styles */
</style>
