import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { 
  admin<PERSON><PERSON>in<PERSON><PERSON><PERSON>, 
  adminAuthMiddleware, 
  userRegisterHandler, 
  user<PERSON>oginHandler, 
  userAuthMiddleware 
} from '../lib/auth.js';
import { authSchemas } from '../schemas/openapi-schemas.js';
import { prisma } from '../lib/prisma.js';

export async function authRoutes(fastify: FastifyInstance) {
  // Route to serve the admin login page
  fastify.get('/admin/login', {
    schema: {
      tags: ['Admin Authentication'],
      summary: 'Admin Login Page',
      description: 'Serves the HTML page for admin login. If already logged in (cookie present), may redirect to an admin dashboard.',
      response: {
        '200': {
          description: 'Admin login HTML page.',
          content: {
            'text/html': {
              schema: { type: 'string' },
            },
          },
        },
        '302': {
          description: 'Redirects to admin dashboard if already authenticated.',
        }
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    // Check if already logged in, redirect to dashboard if so
    if (request.cookies.admin_token) {
        try {
            return reply.status(302).redirect('/admin/dashboard');
        } catch (e) {
            reply.clearCookie('admin_token', { path: '/' });
        }
    }
    return reply.view('pages/admin/login.hbs', {
      title: 'Admin login',
    });
  });

  // Route to handle admin login form submission
  fastify.post('/admin/login', {
    schema: {
      tags: ['Admin Authentication'],
      summary: 'Admin Login Attempt',
      description: 'Handles admin login credentials. On success, sets an HTTP-only cookie `admin_token` and returns a success message with the token.',
      body: authSchemas.AdminLoginPayload,
      response: {
        '200': {
          description: 'Admin login successful. Cookie `admin_token` is set.',
          content: {
            'application/json': {
              schema: authSchemas.AdminLoginSuccessResponse,
            },
          },
        },
        '400': { 
          description: 'Missing username or password.', 
          content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
        },
        '401': { 
          description: 'Invalid credentials.', 
          content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
        },
        '500': { 
          description: 'Internal server error during authentication.', 
          content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
        },
      },
    },
  }, adminLoginHandler);

  // Admin logout route
  fastify.post('/admin/logout', {
    schema: {
      tags: ['Admin Authentication'],
      summary: 'Admin Logout',
      description: 'Clears the admin authentication cookie and redirects to the login page.',
      response: {
        '302': {
          description: 'Successfully logged out, redirects to /admin/login. Clears `admin_token` cookie.',
          headers: {
            Location: {
              schema: { type: 'string' },
              description: 'URL to redirect to (typically /admin/login)'
            }
          }
        },
        '401': { $ref: 'ErrorResponse#' }
      }
    }
  }, async (request, reply) => {
    reply.clearCookie('admin_token', { path: '/' });
    return reply.redirect('/admin/login');
  });

  // Example protected admin route (dashboard placeholder)
  fastify.get('/admin/dashboard', { 
    preHandler: [adminAuthMiddleware],
    schema: {
      tags: ['Admin Management'],
      summary: 'Admin Dashboard',
      description: 'Protected admin dashboard page.',
      security: [{ adminAuth: [] }],
      response: {
        '200': {
          description: 'Admin dashboard HTML page.',
          content: {
            'text/html': {
              schema: { type: 'string' },
            },
          },
        },
        '401': { $ref: 'ErrorResponse#' },
        '302': {
          description: 'Redirects to login if not authenticated.',
        }
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    return reply.view('pages/admin/dashboard.hbs', {
      title: 'Admin Dashboard',
      stats: {
        totalDomains: 0,
        verifiedDomains: 0,
        pendingDomains: 0,
        todayEmails: 0
      },
      user: { name: 'Admin' }
    });
  });

  // =============================================================================
  // USER AUTHENTICATION ROUTES
  // =============================================================================

  // User registration page
  fastify.get('/register', {
    schema: {
      tags: ['User Authentication'],
      summary: 'User Registration Page',
      description: 'Serves the HTML page for user registration.',
      response: {
        '200': {
          description: 'User registration HTML page.',
          content: {
            'text/html': {
              schema: { type: 'string' },
            },
          },
        },
        '302': {
          description: 'Redirects to dashboard if already authenticated.',
        }
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    // Check if already logged in, redirect to dashboard if so
    if (request.cookies.user_token) {
      try {
        return reply.status(302).redirect('/domains');
      } catch (e) {
        reply.clearCookie('user_token', { path: '/' });
      }
    }
    return reply.view('pages/vue/register.hbs', {
      title: 'Create Account',
      isDevelopment: process.env.NODE_ENV === 'development'
    });
  });

  // User registration form submission
  fastify.post('/register', {
    schema: {
      tags: ['User Authentication'],
      summary: 'User Registration',
      description: 'Creates a new user account with email verification disabled.',
      body: {
        type: 'object',
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 8 },
          name: { type: 'string' }
        },
        required: ['email', 'password']
      },
      response: {
        '200': {
          description: 'Registration successful. Cookie `user_token` is set.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: { type: 'string' },
                  user: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      email: { type: 'string' },
                      name: { type: 'string' },
                      monthlyEmailLimit: { type: 'integer' },
                      planType: { type: 'string' }
                    }
                  },
                  token: { type: 'string' }
                }
              }
            },
          },
        },
        '400': { 
          description: 'Validation errors.', 
          content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
        },
        '409': { 
          description: 'User already exists.', 
          content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
        },
        '500': { 
          description: 'Internal server error.', 
          content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
        },
      },
    },
  }, userRegisterHandler);

  // User login page
  fastify.get('/login', {
    schema: {
      tags: ['User Authentication'],
      summary: 'User Login Page',
      description: 'Serves the HTML page for user login.',
      response: {
        '200': {
          description: 'User login HTML page.',
          content: {
            'text/html': {
              schema: { type: 'string' },
            },
          },
        },
        '302': {
          description: 'Redirects to dashboard if already authenticated.',
        }
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    // Check if already logged in, redirect to dashboard if so
    if (request.cookies.user_token) {
      try {
        return reply.status(302).redirect('/domains');
      } catch (e) {
        reply.clearCookie('user_token', { path: '/' });
      }
    }
    return reply.view('pages/vue/login.hbs', {
      title: 'Sign In',
      isDevelopment: process.env.NODE_ENV === 'development'
    });
  });

  // User login form submission
  fastify.post('/login', {
    schema: {
      tags: ['User Authentication'],
      summary: 'User Login',
      description: 'Authenticates user credentials and sets session cookie.',
      body: {
        type: 'object',
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string' }
        },
        required: ['email', 'password']
      },
      response: {
        '200': {
          description: 'Login successful. Cookie `user_token` is set.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: { type: 'string' },
                  user: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      email: { type: 'string' },
                      name: { type: 'string' },
                      monthlyEmailLimit: { type: 'integer' },
                      planType: { type: 'string' }
                    }
                  },
                  token: { type: 'string' }
                }
              }
            },
          },
        },
        '400': { 
          description: 'Missing email or password.', 
          content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
        },
        '401': { 
          description: 'Invalid credentials.', 
          content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
        },
        '500': { 
          description: 'Internal server error.', 
          content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}
        },
      },
    },
  }, userLoginHandler);

  // User logout route
  fastify.post('/user/logout', {
    schema: {
      tags: ['User Authentication'],
      summary: 'User Logout',
      description: 'Clears the user authentication cookie and redirects to the login page.',
      response: {
        '302': {
          description: 'Successfully logged out, redirects to /login. Clears `user_token` cookie.',
        }
      }
    }
  }, async (request, reply) => {
    reply.clearCookie('user_token', { path: '/' });
    return reply.redirect('/login');
  });

  // User dashboard (placeholder)
  fastify.get('/dashboard', { 
    preHandler: [userAuthMiddleware],
    schema: {
      tags: ['User Dashboard'],
      summary: 'User Dashboard',
      description: 'Protected user dashboard page.',
      response: {
        '200': {
          description: 'User dashboard HTML page.',
          content: {
            'text/html': {
              schema: { type: 'string' },
            },
          },
        },
        '401': { $ref: 'ErrorResponse#' },
        '302': {
          description: 'Redirects to login if not authenticated.',
        }
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = (request as any).user;

      // Simplified dashboard - just get basic user info
      const userWithUsage = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          email: true,
          name: true,
          monthlyEmailLimit: true,
          currentMonthEmails: true,
          planType: true
        }
      });

      if (!userWithUsage) {
        return reply.status(404).send({ error: 'User not found' });
      }

      // Get user's current usage with proper counts
      const userWithCounts = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          email: true,
          name: true,
          monthlyEmailLimit: true,
          currentMonthEmails: true,
          planType: true,
          _count: {
            select: {
              domains: true,
              webhooks: true,
            }
          }
        }
      });

      // Get alias count (through domain relationship)
      const aliasCount = await prisma.alias.count({
        where: {
          domain: {
            userId: user.id
          }
        }
      });

      // Get domains with relations for dashboard table
      let domains = [];
      let availableWebhooks = [];
      let aliases = [];

      if (userWithCounts?._count?.domains > 0) {
        domains = await prisma.domain.findMany({
          where: { userId: user.id },
          include: {
            webhook: true,
            aliases: {
              where: { active: true }
            }
          },
          orderBy: { createdAt: 'desc' }
        });
      }

      // Get all user's webhooks for dropdown options and webhooks table
      availableWebhooks = await prisma.webhook.findMany({
        where: { userId: user.id },
        select: {
          id: true,
          name: true,
          url: true,
          description: true,
          verified: true,
          createdAt: true
        },
        orderBy: { name: 'asc' }
      });

      // Get all user's aliases for aliases table
      aliases = await prisma.alias.findMany({
        where: {
          domain: {
            userId: user.id
          }
        },
        include: {
          domain: {
            select: {
              domain: true,
              id: true
            }
          },
          webhook: {
            select: {
              name: true,
              url: true,
              id: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      const activeTab = (request.query as any)?.tab || 'domains';
      const counts = {
        domains: userWithCounts?._count?.domains || 0,
        aliases: aliasCount,
        webhooks: userWithCounts?._count?.webhooks || 0
      };

      const dashboardData = {
        activeTab,
        counts,
        domains: domains,
        aliases: aliases,
        webhooks: availableWebhooks
      };

      return reply.view('pages/vue/dashboard.hbs', {
        title: 'Dashboard',
        user: userWithCounts,
        usagePercentage: Math.round((userWithCounts?.currentMonthEmails || 0) / (userWithCounts?.monthlyEmailLimit || 50) * 100),
        activeTab,
        domains: domains,
        availableWebhooks: availableWebhooks,
        aliases: aliases,
        webhooks: availableWebhooks, // Same data, different context
        counts,
        metrics: {
          emails_processed_24h: 0, // Will be populated by client-side API call
          success_rate: 100
        },
        dashboardData: JSON.stringify(dashboardData),
        isDevelopment: process.env.NODE_ENV === 'development'
      });
    } catch (error: any) {
      fastify.log.error({ error: error?.message, stack: error.stack }, 'Dashboard error');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Dashboard temporarily unavailable'
      });
    }
  });

  // Route aliases for better UX - serve SPA directly
  fastify.get('/domains', {
    preHandler: [userAuthMiddleware],
    schema: {
      tags: ['User Dashboard'],
      summary: 'Domains page',
      description: 'Serves the dashboard SPA with domains view.',
      response: {
        '200': {
          description: 'Dashboard HTML page with domains view.',
          content: {
            'text/html': {
              schema: { type: 'string' },
            },
          },
        },
        '401': { $ref: 'ErrorResponse#' },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    // Serve the dashboard SPA - Vue Router will handle routing to /domains
    try {
      const user = (request as any).user;
      const userWithCounts = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          email: true,
          name: true,
          monthlyEmailLimit: true,
          currentMonthEmails: true,
          planType: true,
          _count: {
            select: {
              domains: true,
              webhooks: true,
            }
          }
        }
      });

      if (!userWithCounts) {
        return reply.status(404).send({ error: 'User not found' });
      }

      const aliasCount = await prisma.alias.count({
        where: { domain: { userId: user.id } }
      });

      const counts = {
        domains: userWithCounts?._count?.domains || 0,
        aliases: aliasCount,
        webhooks: userWithCounts?._count?.webhooks || 0
      };

      const dashboardData = {
        activeTab: 'domains',
        counts
      };

      return reply.view('pages/vue/dashboard.hbs', {
        title: 'Domains - Dashboard',
        user: userWithCounts,
        usagePercentage: Math.round((userWithCounts?.currentMonthEmails || 0) / (userWithCounts?.monthlyEmailLimit || 50) * 100),
        counts,
        dashboardData: JSON.stringify(dashboardData),
        isDevelopment: process.env.NODE_ENV === 'development'
      });
    } catch (error: any) {
      fastify.log.error({ error: error?.message, stack: error.stack }, 'Domains page error');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Dashboard temporarily unavailable'
      });
    }
  });

  fastify.get('/aliases', {
    preHandler: [userAuthMiddleware],
    schema: {
      tags: ['User Dashboard'],
      summary: 'Aliases page',
      description: 'Serves the dashboard SPA with aliases view.',
      response: {
        '200': {
          description: 'Dashboard HTML page with aliases view.',
          content: {
            'text/html': {
              schema: { type: 'string' },
            },
          },
        },
        '401': { $ref: 'ErrorResponse#' },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    // Serve the dashboard SPA - Vue Router will handle routing to /aliases
    try {
      const user = (request as any).user;
      const userWithCounts = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          email: true,
          name: true,
          monthlyEmailLimit: true,
          currentMonthEmails: true,
          planType: true,
          _count: {
            select: {
              domains: true,
              webhooks: true,
            }
          }
        }
      });

      if (!userWithCounts) {
        return reply.status(404).send({ error: 'User not found' });
      }

      const aliasCount = await prisma.alias.count({
        where: { domain: { userId: user.id } }
      });

      const counts = {
        domains: userWithCounts?._count?.domains || 0,
        aliases: aliasCount,
        webhooks: userWithCounts?._count?.webhooks || 0
      };

      const dashboardData = {
        activeTab: 'aliases',
        counts
      };

      return reply.view('pages/vue/dashboard.hbs', {
        title: 'Aliases - Dashboard',
        user: userWithCounts,
        usagePercentage: Math.round((userWithCounts?.currentMonthEmails || 0) / (userWithCounts?.monthlyEmailLimit || 50) * 100),
        counts,
        dashboardData: JSON.stringify(dashboardData),
        isDevelopment: process.env.NODE_ENV === 'development'
      });
    } catch (error: any) {
      fastify.log.error({ error: error?.message, stack: error.stack }, 'Aliases page error');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Dashboard temporarily unavailable'
      });
    }
  });

  fastify.get('/webhooks', {
    preHandler: [userAuthMiddleware],
    schema: {
      tags: ['User Dashboard'],
      summary: 'Webhooks page',
      description: 'Serves the dashboard SPA with webhooks view.',
      response: {
        '200': {
          description: 'Dashboard HTML page with webhooks view.',
          content: {
            'text/html': {
              schema: { type: 'string' },
            },
          },
        },
        '401': { $ref: 'ErrorResponse#' },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    // Serve the dashboard SPA - Vue Router will handle routing to /webhooks
    try {
      const user = (request as any).user;
      const userWithCounts = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          email: true,
          name: true,
          monthlyEmailLimit: true,
          currentMonthEmails: true,
          planType: true,
          _count: {
            select: {
              domains: true,
              webhooks: true,
            }
          }
        }
      });

      if (!userWithCounts) {
        return reply.status(404).send({ error: 'User not found' });
      }

      const aliasCount = await prisma.alias.count({
        where: { domain: { userId: user.id } }
      });

      const counts = {
        domains: userWithCounts?._count?.domains || 0,
        aliases: aliasCount,
        webhooks: userWithCounts?._count?.webhooks || 0
      };

      const dashboardData = {
        activeTab: 'webhooks',
        counts
      };

      return reply.view('pages/vue/dashboard.hbs', {
        title: 'Webhooks - Dashboard',
        user: userWithCounts,
        usagePercentage: Math.round((userWithCounts?.currentMonthEmails || 0) / (userWithCounts?.monthlyEmailLimit || 50) * 100),
        counts,
        dashboardData: JSON.stringify(dashboardData),
        isDevelopment: process.env.NODE_ENV === 'development'
      });
    } catch (error: any) {
      fastify.log.error({ error: error?.message, stack: error.stack }, 'Webhooks page error');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Dashboard temporarily unavailable'
      });
    }
  });

  fastify.get('/logs', {
    preHandler: [userAuthMiddleware],
    schema: {
      tags: ['User Dashboard'],
      summary: 'Logs page',
      description: 'Serves the dashboard SPA with logs view.',
      response: {
        '200': {
          description: 'Dashboard HTML page with logs view.',
          content: {
            'text/html': {
              schema: { type: 'string' },
            },
          },
        },
        '401': { $ref: 'ErrorResponse#' },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    // Serve the dashboard SPA - Vue Router will handle routing to /logs
    try {
      const user = (request as any).user;
      const userWithCounts = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          email: true,
          name: true,
          monthlyEmailLimit: true,
          currentMonthEmails: true,
          planType: true,
          _count: {
            select: {
              domains: true,
              webhooks: true,
            }
          }
        }
      });

      if (!userWithCounts) {
        return reply.status(404).send({ error: 'User not found' });
      }

      const aliasCount = await prisma.alias.count({
        where: { domain: { userId: user.id } }
      });

      const counts = {
        domains: userWithCounts?._count?.domains || 0,
        aliases: aliasCount,
        webhooks: userWithCounts?._count?.webhooks || 0
      };

      const dashboardData = {
        activeTab: 'logs',
        counts
      };

      return reply.view('pages/vue/dashboard.hbs', {
        title: 'Logs - Dashboard',
        user: userWithCounts,
        usagePercentage: Math.round((userWithCounts?.currentMonthEmails || 0) / (userWithCounts?.monthlyEmailLimit || 50) * 100),
        counts,
        dashboardData: JSON.stringify(dashboardData),
        isDevelopment: process.env.NODE_ENV === 'development'
      });
    } catch (error: any) {
      fastify.log.error({ error: error?.message, stack: error.stack }, 'Logs page error');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Dashboard temporarily unavailable'
      });
    }
  });

  // Settings page
  fastify.get('/settings', {
    preHandler: [userAuthMiddleware],
    schema: {
      tags: ['User Dashboard'],
      summary: 'User Settings Page',
      description: 'Serves the user settings page.',
      response: {
        '200': {
          description: 'User settings HTML page.',
          content: {
            'text/html': {
              schema: { type: 'string' },
            },
          },
        },
        '401': { $ref: 'ErrorResponse#' },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const user = (request as any).user;
    return reply.view('pages/vue/dashboard.hbs', {
      title: 'Settings',
      user: user,
      isDevelopment: process.env.NODE_ENV === 'development'
    });
  });

  fastify.log.info('User authentication routes registered');
  fastify.log.info('Admin routes registered under /admin');
}